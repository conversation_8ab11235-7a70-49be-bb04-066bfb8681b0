# 💸 Paytm Wallet Clone

A web application inspired by Paytm Wallet, built with modern web technologies and organized using a monorepo structure.

---

## 🧱 Tech Stack

- **Monorepo:** [Turborepo](https://turbo.build/repo)
- **Frontend:** [Next.js](https://nextjs.org/)
- **Backend:**
  - Basic app logic using **Next.js API routes**
  - **Node.js** server for handling webhooks
- **Styling:** [Tailwind CSS](https://tailwindcss.com/)
- **UI Components:** [shadcn/ui](https://ui.shadcn.dev/)
- **State Management:** [Jo<PERSON>](https://jotai.org/)
- **Database:** [Prisma](https://www.prisma.io/) with **PostgreSQL**
- **Authentication:** [NextAuth.js v5](https://next-auth.js.org/) with Prisma Adapter

---

## 🗂️ Project Structure

This project follows a monorepo structure using **Turborepo**. Most of the shareable logic and dependencies (like utilities, types, store(state management), Database, config) live inside the `packages/` folder for reusability across apps.

---

## 🚀 Goals

- Modular and scalable architecture using **Turborepo**
- Clean UI with **shadcn/ui** and **Tailwind CSS**
- Robust authentication using **NextAuth v5** and **Prisma Adapter**
- Smooth state management with **Jotai**
- Reliable data handling via **PostgreSQL** and **Prisma ORM**
- Extendable backend with webhook support using **Node.js**

---

## 🛠️ Work In Progress

This project is under active development. The focus is on:

- Making the best use of **Turborepo**
- Keeping shared logic DRY and reusable
- Building a seamless user experience

---

## 📦 Installation

Coming soon...

---

## 🧪 Testing

Coming soon...

---

## 📄 License

MIT
