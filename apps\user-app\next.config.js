/** @type {import('next').NextConfig} */
const nextConfig = {
    transpilePackages: ["@repo/ui"],
    images: {
      domains: ["avatars.githubusercontent.com"],
    },
    serverExternalPackages: ['@prisma/client', '@prisma/engines'],
    webpack: (config, { isServer }) => {
      if (isServer) {
        config.externals.push('@prisma/client')
      }
      return config
    }
  }

  export default nextConfig
  
