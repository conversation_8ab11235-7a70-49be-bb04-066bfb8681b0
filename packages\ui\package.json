{"name": "@repo/ui", "version": "0.0.0", "type": "module", "private": true, "scripts": {"lint": "eslint . --max-warnings 0"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tooltip": "^1.2.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "input-otp": "^1.4.2", "lucide-react": "^0.475.0", "next-themes": "^0.4.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-resizable-panels": "^2.1.9", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.7", "zod": "^3.24.2"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@tailwindcss/postcss": "^4.0.8", "@turbo/gen": "^2.4.2", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4.0.8", "typescript": "^5.7.3"}, "exports": {"./globals.css": "./src/styles/globals.css", "./postcss.config": "./postcss.config.mjs", "./lib/*": "./src/lib/*.ts", "./components/*": "./src/components/*.tsx", "./hooks/*": "./src/hooks/*.ts", "./pages/*": "./src/pages/*.tsx"}}