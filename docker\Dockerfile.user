FROM node:18-alpine

RUN npm install -g pnpm

WORKDIR /app

# Copy root config files for dependency resolution
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml turbo.json ./

# Copy the rest of the monorepo
COPY apps ./apps
COPY packages ./packages

# Set environment for production build
ENV NODE_ENV=production

# Install dependencies (include devDependencies for build)
RUN pnpm install

# Generate Prisma client 
RUN pnpm --filter @repo/db db:generate

# Build the Node.js app 
RUN pnpm run --filter @repo/db build
RUN pnpm run --filter @repo/user build

# Start the app
CMD ["pnpm", "--filter", "@repo/user", "start"]
