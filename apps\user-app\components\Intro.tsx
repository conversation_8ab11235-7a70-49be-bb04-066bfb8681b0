"use client";

import { content } from "@/lib";
import { useRouter } from "next/navigation";

export const Intro = () => {
  return (
    <div className="w-full h-[60vh] pt-20 px-6 flex flex-col gap-4 ">
      <TopBar />
      <div className="sm:text-2xl font-bold sm:ml-5 text-l">💳 Payments App – Simplified Money Transfers</div>
      <div>
        <p className="max-w-3xl text-sm sm:text-lg text-accent-foreground sm:ml-5">{content}</p>
        <div>
          <image src="/payments.svg" alt="Payments" className="w-full h-auto mt-10 rounded-lg shadow-md" />
        </div>
      </div>
    </div>
  );
};

const TopBar = () => {
  const router = useRouter();
  return (
    <div className="fixed top-0 left-0 w-full z-50 shadow-md bg-background">
      <div className="max-w-7xl mx-auto px-4 py-3 flex items-center justify-between">
        <div className="text-xl font-semibold">PayMents</div>
        <button
          onClick={() => router.push("/signin")}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
        >
          Login
        </button>
      </div>
    </div>
  );
};
