"use client";

import { content } from "@/lib";
import { useRouter } from "next/navigation";
import Image from 'next/image'

export const Intro = () => {
  return (
    <div className="w-full h-[60vh] px-6 pt-20 md:pt-10 lg:pt-0">
      <TopBar />
      <One/>
      <Two/>
    </div>
  );
};


const TopBar = () => {
  const router = useRouter();
  return (
    <div className="fixed top-0 left-0 w-full z-50 shadow-md bg-background">
      <div className="max-w-7xl mx-auto px-4 py-3 flex items-center justify-between">
        <div className="text-xl ml-4 font-semibold">PayMents</div>
        <button
          onClick={() => router.push("/signin")}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
        >
          Login
        </button>
      </div>
    </div>
  );
};

const One = () => {
  return(
  <div className="one flex flex-col mt-10 md:flex-row gap-8 items-center justify-between max-w-7xl mx-auto md:mt-5">
        {/* Text Content */}
        <div className="flex-1">
          <div className="text-xl sm:text-2xl font-bold mb-4">
            💳 Payments App – Simplified Money Transfers
          </div>
          <p className="max-w-xl text-sm sm:text-lg text-accent-foreground">{content}</p>
        </div>

        {/* Image */}
        <div className="flex-1">
          <Image
            src="/payments.svg"
            alt="Payments"
            width={800}
            height={600}
            className="w-full h-auto rounded-lg shadow-md"
          />
        </div>
      </div>
    );
}



const Two = () => {
  return (
    <div className="w-full px-6 py-12 max-w-7xl mx-auto flex flex-col gap-12">
      {/* Section: Dashboard */}
      <div className="flex flex-col gap-4">
        <h2 className="text-2xl font-semibold">🚀 Get Started</h2>
        <p className="text-muted-foreground text-base">
          After logging in or signing up, you will be redirected to your dashboard.
        </p>
        <Image
          src="/dashboard.png"
          alt="Dashboard"
          width={400}
          height={300}
          className="w-full h-auto rounded-lg shadow-md"
        />
      </div>

      {/* Section: Profile & Wallet Setup */}
      <div className="flex flex-col gap-4">
        <h3 className="text-xl font-medium">🛠 Set Up Your Profile</h3>
        <p className="text-accent-foreground text-base">
          Navigate to the profile page to generate your Paytm ID and set your wallet PIN for secure transactions.
        </p>

        <Image
          src="/profile.png"
          alt="Profile"
          width={800}
          height={600}
          className="w-1/2 h-auto rounded-lg shadow-md"
        />
        <Image
          src="/walletpin.png"
          alt="Wallet Pin"
          width={800}
          height={600}
          className="w-1/3 h-auto rounded-lg shadow-md"
        />
      </div>
    </div>
  );
};

export default Two;
