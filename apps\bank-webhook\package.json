{"name": "@repo/bankwebhook", "version": "1.0.0", "description": "", "type": "module", "scripts": {"build": "tsc -b", "start": "node dist/index.js", "dev": "pnpm build && pnpm start"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.9.0", "devDependencies": {"@repo/db": "workspace:*", "@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "^22.14.1", "esbuild": "^0.25.4", "eslint": "^9.24.0", "typescript": "^5.8.3"}, "dependencies": {"@types/express": "^5.0.1", "express": "^5.1.0", "zod": "^3.24.3"}}