{"name": "@repo/db", "version": "1.0.0", "type": "module", "scripts": {"build": "tsc -b", "postbuild": "node -e \"require('fs').cpSync('src/generated', 'dist/generated', {recursive: true})\"", "postinstall": "prisma generate", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev --skip-generate", "db:deploy": "prisma migrate deploy"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "@types/node": "^22.14.1", "prisma": "^6.6.0"}, "dependencies": {"@auth/prisma-adapter": "^2.9.0", "@prisma/client": "6.6.0", "@prisma/extension-accelerate": "^2.0.1"}, "exports": {"./prisma": "./dist/index.js"}, "main": "./dist/index.js", "types": "./dist/index.d.ts"}