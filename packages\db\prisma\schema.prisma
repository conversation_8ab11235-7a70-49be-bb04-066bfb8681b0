// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}
generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

model User {
  id            String          @id @default(cuid())
  name          String?
  email         String          @unique
  emailVerified DateTime?
  image         String?
  paymentId     String?         @unique
  walletPin     String?         
  walletKey     String?
  OnRampTransaction OnRampTransaction[]
  sentTransfers WalletTransaction[] @relation(name: "FromUserRelation")
  receivedTransfers WalletTransaction[] @relation(name: "ToUserRelation")
  WBalance      WalletBalance?
  BBalance      BankBalance?

  accounts      Account[]
  sessions      Session[]
  // Optional for WebAuthn support
  Authenticator Authenticator[]
 
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model OnRampTransaction {
  id            String          @id @default(cuid())
  status        OnRampStatus
  type          TransactionType
  token         String          @unique
  provider      String
  amount        Int
  startTime     DateTime
  userId        String
  user          User            @relation(fields: [userId], references: [id])
}

model WalletTransaction {
  id            String          @id @default(cuid())
  amount        Int             
  createdAt     DateTime        @default(now())
  fromUserId    String
  fromUser      User            @relation(name: "FromUserRelation", fields: [fromUserId], references: [id])
  toUserId      String
  toUser        User            @relation(name: "ToUserRelation", fields: [toUserId], references: [id])
}

enum OnRampStatus {
  Success
  Failure
  Processing
}

enum TransactionType {
  Credit
  Debit
}

model BankBalance {
  id            String          @id @default(cuid())
  userId        String          @unique
  amount        Int
  locked        Int
  user          User            @relation(fields: [userId], references: [id])

}

model WalletBalance {
  id            String          @id @default(cuid())
  userId        String          @unique
  amount        Int
  locked        Int
  user          User            @relation(fields: [userId], references: [id])

}

model WalletKey {
  key           String          @unique
}
 
model Account {
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
 
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
 
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
 
  @@id([provider, providerAccountId])
}
 
model Session {
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
 
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}
 
model VerificationToken {
  identifier String
  token      String
  expires    DateTime
 
  @@id([identifier, token])
}
 
// Optional for WebAuthn support
model Authenticator {
  credentialID         String  @unique
  userId               String
  providerAccountId    String
  credentialPublicKey  String
  counter              Int
  credentialDeviceType String
  credentialBackedUp   Boolean
  transports           String?
 
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
 
  @@id([userId, credentialID])
}
