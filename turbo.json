{"$schema": "https://turbo.build/schema.json", "ui": "tui", "tasks": {"db:generate": {"cache": false}, "db:migrate": {"cache": false, "persistent": true}, "db:deploy": {"cache": false}, "build": {"dependsOn": ["^build", "^db:generate"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".next/**", "!.next/cache/**"]}, "lint": {"dependsOn": ["^lint"]}, "check-types": {"dependsOn": ["^check-types"]}, "dev": {"dependsOn": ["^db:generate"], "cache": false, "persistent": true}}}