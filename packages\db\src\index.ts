import { PrismaAdapter } from "@auth/prisma-adapter";
import { PrismaClient, Prisma, $Enums } from "@prisma/client/edge";

const globalForPrisma = globalThis as unknown as { prisma: PrismaClient };

export const prisma = globalForPrisma.prisma || new PrismaClient({
  log: process.env.NODE_ENV === "development" ? ["query", "error", "warn"] : ["error"],
});

if (process.env.NODE_ENV !== "production") {
  globalForPrisma.prisma = prisma;
}

export {Prisma, $Enums};
export const adapter: any = PrismaAdapter(prisma);
