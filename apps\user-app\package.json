{"name": "@repo/user", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack --port 3000", "build": "next build", "start": "next start", "lint": "next lint --max-warnings 0", "check-types": "tsc --noEmit"}, "dependencies": {"@prisma/client": "6.6.0", "@repo/db": "workspace:*", "@repo/ui": "workspace:*", "@tailwindcss/postcss": "^4.0.8", "axios": "^1.9.0", "bcrypt": "^6.0.0", "install": "^0.13.0", "jotai": "^2.12.3", "lucide-react": "^0.475.0", "next": "^15.3.0", "next-auth": "5.0.0-beta.26", "next-themes": "^0.4.4", "postcss": "^8.5.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.1", "tailwindcss": "^4.0.8", "zod": "^3.25.30"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/bcrypt": "^5.0.2", "@types/node": "^22.14.1", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "eslint": "^9.24.0", "typescript": "5.8.2"}}